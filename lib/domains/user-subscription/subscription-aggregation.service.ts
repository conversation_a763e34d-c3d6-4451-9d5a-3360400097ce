import { 
  UserSubscriptionEntry,
  AggregatedSubscriptionState,
  SubscriptionPlan,
  SubscriptionStatus,
  getActiveEntry,
  SUBSCRIPTION_LIMITS
} from "./user-subscription.types"
import { UserSubscriptionEntryService } from "./user-subscription-entry.service"
import { SubscriptionPrecedenceService } from "./subscription-precedence.service"
import { AI_USAGE_LIMITS } from "../user-ai-usage/user-ai-usage.types"

/**
 * Service for aggregating subscription data from multiple sources
 */
export class SubscriptionAggregationService {
  
  /**
   * Get comprehensive subscription state for a user
   */
  static async getComprehensiveSubscriptionState(userId: string): Promise<AggregatedSubscriptionState> {
    try {
      // Force recalculate precedence to ensure up-to-date state
      await SubscriptionPrecedenceService.forceRecalculatePrecedence(userId)
      
      // Get aggregated state
      return await UserSubscriptionEntryService.getAggregatedSubscriptionState(userId)
    } catch (error) {
      console.error("Error getting comprehensive subscription state:", error)
      return this.getDefaultSubscriptionState()
    }
  }

  /**
   * Get subscription limits considering all active subscriptions and perks
   */
  static async getEnhancedSubscriptionLimits(userId: string): Promise<{
    maxSquads: number
    maxTripsPerSquad: number
    maxDailyAIRequests: number
    maxWeeklyAIRequests: number
    hasTripChat: boolean
    perkEnhancements: {
      additionalSquads: number
      additionalTripsPerSquad: number
      additionalDailyAI: number
      additionalWeeklyAI: number
      activePerkIds: string[]
    } | null
  }> {
    try {
      const subscriptionState = await this.getComprehensiveSubscriptionState(userId)
      
      // Get base limits from subscription
      const baseLimits = subscriptionState.isSubscribed 
        ? SUBSCRIPTION_LIMITS.PRO 
        : SUBSCRIPTION_LIMITS.FREE

      // TODO: Integrate with perk system to calculate enhancements
      // For now, return base limits
      return {
        maxSquads: baseLimits.MAX_SQUADS,
        maxTripsPerSquad: baseLimits.MAX_TRIPS_PER_SQUAD,
        maxDailyAIRequests: baseLimits.MAX_DAILY_AI_REQUESTS,
        maxWeeklyAIRequests: baseLimits.MAX_WEEKLY_AI_REQUESTS,
        hasTripChat: baseLimits.HAS_TRIP_CHAT,
        perkEnhancements: subscriptionState.perkEnhancements
      }
    } catch (error) {
      console.error("Error getting enhanced subscription limits:", error)
      return {
        maxSquads: SUBSCRIPTION_LIMITS.FREE.MAX_SQUADS,
        maxTripsPerSquad: SUBSCRIPTION_LIMITS.FREE.MAX_TRIPS_PER_SQUAD,
        maxDailyAIRequests: SUBSCRIPTION_LIMITS.FREE.MAX_DAILY_AI_REQUESTS,
        maxWeeklyAIRequests: SUBSCRIPTION_LIMITS.FREE.MAX_WEEKLY_AI_REQUESTS,
        hasTripChat: SUBSCRIPTION_LIMITS.FREE.HAS_TRIP_CHAT,
        perkEnhancements: null
      }
    }
  }

  /**
   * Check if user has access to a specific feature
   */
  static async hasFeatureAccess(userId: string, feature: string): Promise<boolean> {
    try {
      const subscriptionState = await this.getComprehensiveSubscriptionState(userId)
      
      switch (feature) {
        case "trip_chat":
          return subscriptionState.isSubscribed
        case "unlimited_ai":
          return subscriptionState.isSubscribed
        case "multiple_squads":
          const limits = await this.getEnhancedSubscriptionLimits(userId)
          return limits.maxSquads > 1
        default:
          return false
      }
    } catch (error) {
      console.error("Error checking feature access:", error)
      return false
    }
  }

  /**
   * Get subscription summary for display purposes
   */
  static async getSubscriptionSummary(userId: string): Promise<{
    isSubscribed: boolean
    currentPlan: SubscriptionPlan
    currentSource: string | null
    expiresAt: Date | null
    nextBillingDate: Date | null
    pausedSubscriptions: Array<{
      source: string
      plan: SubscriptionPlan
      pausedAt: Date
    }>
    upcomingExpirations: Array<{
      source: string
      plan: SubscriptionPlan
      expiresAt: Date
    }>
  }> {
    try {
      const subscriptionState = await this.getComprehensiveSubscriptionState(userId)
      const precedenceSummary = await SubscriptionPrecedenceService.getSubscriptionPrecedenceSummary(userId)
      
      // Calculate expiration date
      let expiresAt: Date | null = null
      if (subscriptionState.activeEntry?.endDate) {
        expiresAt = subscriptionState.activeEntry.endDate instanceof Date 
          ? subscriptionState.activeEntry.endDate 
          : subscriptionState.activeEntry.endDate.toDate()
      }

      // Calculate next billing date (for Stripe subscriptions)
      let nextBillingDate: Date | null = null
      if (subscriptionState.activeEntry?.stripeData?.currentPeriodEnd) {
        const periodEnd = subscriptionState.activeEntry.stripeData.currentPeriodEnd
        nextBillingDate = periodEnd instanceof Date ? periodEnd : periodEnd.toDate()
      }

      // Get paused subscriptions
      const pausedSubscriptions = precedenceSummary.pausedEntries.map(entry => ({
        source: entry.source,
        plan: entry.subscriptionPlan,
        pausedAt: entry.updatedAt instanceof Date ? entry.updatedAt : entry.updatedAt?.toDate() || new Date()
      }))

      // Get upcoming expirations
      const upcomingExpirations = subscriptionState.allEntries
        .filter(entry => entry.endDate && entry.status !== "expired")
        .map(entry => ({
          source: entry.source,
          plan: entry.subscriptionPlan,
          expiresAt: entry.endDate instanceof Date ? entry.endDate : entry.endDate!.toDate()
        }))
        .sort((a, b) => a.expiresAt.getTime() - b.expiresAt.getTime())

      return {
        isSubscribed: subscriptionState.isSubscribed,
        currentPlan: subscriptionState.subscriptionPlan,
        currentSource: subscriptionState.activeEntry?.source || null,
        expiresAt,
        nextBillingDate,
        pausedSubscriptions,
        upcomingExpirations
      }
    } catch (error) {
      console.error("Error getting subscription summary:", error)
      return {
        isSubscribed: false,
        currentPlan: "free",
        currentSource: null,
        expiresAt: null,
        nextBillingDate: null,
        pausedSubscriptions: [],
        upcomingExpirations: []
      }
    }
  }

  /**
   * Check subscription health and return warnings
   */
  static async checkSubscriptionHealth(userId: string): Promise<{
    isHealthy: boolean
    warnings: string[]
    recommendations: string[]
  }> {
    try {
      const subscriptionState = await this.getComprehensiveSubscriptionState(userId)
      const precedenceSummary = await SubscriptionPrecedenceService.getSubscriptionPrecedenceSummary(userId)
      
      const warnings: string[] = []
      const recommendations: string[] = []

      // Check for conflicting subscriptions
      if (precedenceSummary.pausedEntries.length > 0) {
        warnings.push(`${precedenceSummary.pausedEntries.length} subscription(s) are paused due to higher precedence subscriptions`)
      }

      // Check for upcoming expirations
      const now = new Date()
      const sevenDaysFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
      
      for (const entry of subscriptionState.allEntries) {
        if (entry.endDate && entry.status !== "expired") {
          const endDate = entry.endDate instanceof Date ? entry.endDate : entry.endDate.toDate()
          if (endDate <= sevenDaysFromNow) {
            warnings.push(`${entry.source} subscription expires on ${endDate.toLocaleDateString()}`)
            
            if (entry.source === "perk" || entry.source === "giveaway") {
              recommendations.push("Consider upgrading to a paid subscription before your free period expires")
            }
          }
        }
      }

      // Check for expired entries that should be cleaned up
      if (precedenceSummary.expiredEntries.length > 0) {
        recommendations.push("Some expired subscription entries can be cleaned up")
      }

      return {
        isHealthy: warnings.length === 0,
        warnings,
        recommendations
      }
    } catch (error) {
      console.error("Error checking subscription health:", error)
      return {
        isHealthy: false,
        warnings: ["Unable to check subscription health"],
        recommendations: ["Please contact support if issues persist"]
      }
    }
  }

  /**
   * Get default subscription state
   */
  private static getDefaultSubscriptionState(): AggregatedSubscriptionState {
    return {
      isSubscribed: false,
      subscriptionPlan: "free",
      subscriptionStatus: null,
      currentPeriodEnd: null,
      activeEntry: null,
      allEntries: [],
      perkEnhancements: null
    }
  }

  /**
   * Cleanup expired entries for a user
   */
  static async cleanupExpiredEntries(userId: string): Promise<{
    cleanedCount: number
    errors: string[]
  }> {
    try {
      const precedenceSummary = await SubscriptionPrecedenceService.getSubscriptionPrecedenceSummary(userId)
      const errors: string[] = []
      let cleanedCount = 0

      // Only cleanup expired entries that are older than 30 days
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)

      for (const expiredEntry of precedenceSummary.expiredEntries) {
        const updatedAt = expiredEntry.updatedAt instanceof Date 
          ? expiredEntry.updatedAt 
          : expiredEntry.updatedAt?.toDate()

        if (updatedAt && updatedAt < thirtyDaysAgo) {
          const deleteResult = await UserSubscriptionEntryService.deleteSubscriptionEntry(userId, expiredEntry.id)
          if (deleteResult.success) {
            cleanedCount++
          } else {
            errors.push(`Failed to delete expired entry ${expiredEntry.id}`)
          }
        }
      }

      return { cleanedCount, errors }
    } catch (error) {
      console.error("Error cleaning up expired entries:", error)
      return { cleanedCount: 0, errors: ["Failed to cleanup expired entries"] }
    }
  }
}
