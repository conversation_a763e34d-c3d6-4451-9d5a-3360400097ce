import { 
  collection, 
  doc, 
  getDoc, 
  getDocs, 
  setDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  serverTimestamp,
  runTransaction,
  Timestamp
} from "firebase/firestore"
import { db } from "@/lib/firebase"
import { ServiceResponse } from "../base/base.types"
import {
  UserSubscriptionEntry,
  UserSubscriptionEntryCreateData,
  UserSubscriptionEntryUpdateData,
  AggregatedSubscriptionState,
  SubscriptionSource,
  SubscriptionEntryStatus,
  getActiveEntry,
  isEntryActive,
  isEntryExpired,
  sortEntriesByPrecedence,
  validateSubscriptionEntry,
  SUBSCRIPTION_PRECEDENCE
} from "./user-subscription.types"

/**
 * Service for managing user subscription entries
 */
export class UserSubscriptionEntryService {
  private static readonly COLLECTION = "userSubscriptions"
  private static readonly ENTRIES_SUBCOLLECTION = "entries"

  /**
   * Get all subscription entries for a user
   */
  static async getUserSubscriptionEntries(userId: string): Promise<UserSubscriptionEntry[]> {
    try {
      const entriesRef = collection(db, this.COLLECTION, userId, this.ENTRIES_SUBCOLLECTION)
      const snapshot = await getDocs(entriesRef)
      
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as UserSubscriptionEntry[]
    } catch (error) {
      console.error("Error getting user subscription entries:", error)
      return []
    }
  }

  /**
   * Get a specific subscription entry
   */
  static async getSubscriptionEntry(
    userId: string, 
    entryId: string
  ): Promise<UserSubscriptionEntry | null> {
    try {
      const entryRef = doc(db, this.COLLECTION, userId, this.ENTRIES_SUBCOLLECTION, entryId)
      const entryDoc = await getDoc(entryRef)
      
      if (entryDoc.exists()) {
        return { id: entryDoc.id, ...entryDoc.data() } as UserSubscriptionEntry
      }
      
      return null
    } catch (error) {
      console.error("Error getting subscription entry:", error)
      return null
    }
  }

  /**
   * Get subscription entries by source
   */
  static async getEntriesBySource(
    userId: string, 
    source: SubscriptionSource
  ): Promise<UserSubscriptionEntry[]> {
    try {
      const entriesRef = collection(db, this.COLLECTION, userId, this.ENTRIES_SUBCOLLECTION)
      const q = query(entriesRef, where("source", "==", source))
      const snapshot = await getDocs(q)
      
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as UserSubscriptionEntry[]
    } catch (error) {
      console.error("Error getting entries by source:", error)
      return []
    }
  }

  /**
   * Create a new subscription entry
   */
  static async createSubscriptionEntry(
    entryData: UserSubscriptionEntryCreateData
  ): Promise<ServiceResponse<UserSubscriptionEntry>> {
    try {
      // Validate entry data
      const validationErrors = validateSubscriptionEntry(entryData)
      if (validationErrors.length > 0) {
        return { 
          success: false, 
          error: new Error(`Validation failed: ${validationErrors.join(", ")}`) 
        }
      }

      // For Stripe subscriptions, ensure only one exists per user
      if (entryData.source === "stripe") {
        const existingStripeEntries = await this.getEntriesBySource(entryData.userId, "stripe")
        if (existingStripeEntries.length > 0) {
          return { 
            success: false, 
            error: new Error("User already has a Stripe subscription entry") 
          }
        }
      }

      const entryRef = doc(collection(db, this.COLLECTION, entryData.userId, this.ENTRIES_SUBCOLLECTION))
      
      await setDoc(entryRef, {
        ...entryData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      })

      const createdEntry: UserSubscriptionEntry = {
        id: entryRef.id,
        ...entryData,
        createdAt: null, // Will be set by server
        updatedAt: null,
      }

      return { success: true, id: entryRef.id, data: createdEntry }
    } catch (error) {
      console.error("Error creating subscription entry:", error)
      return { success: false, error }
    }
  }

  /**
   * Update a subscription entry
   */
  static async updateSubscriptionEntry(
    userId: string,
    entryId: string,
    updateData: UserSubscriptionEntryUpdateData
  ): Promise<ServiceResponse> {
    try {
      const entryRef = doc(db, this.COLLECTION, userId, this.ENTRIES_SUBCOLLECTION, entryId)
      
      await updateDoc(entryRef, {
        ...updateData,
        updatedAt: serverTimestamp(),
      })

      return { success: true }
    } catch (error) {
      console.error("Error updating subscription entry:", error)
      return { success: false, error }
    }
  }

  /**
   * Delete a subscription entry
   */
  static async deleteSubscriptionEntry(
    userId: string, 
    entryId: string
  ): Promise<ServiceResponse> {
    try {
      const entryRef = doc(db, this.COLLECTION, userId, this.ENTRIES_SUBCOLLECTION, entryId)
      await deleteDoc(entryRef)
      
      return { success: true }
    } catch (error) {
      console.error("Error deleting subscription entry:", error)
      return { success: false, error }
    }
  }

  /**
   * Update entry status
   */
  static async updateEntryStatus(
    userId: string,
    entryId: string,
    status: SubscriptionEntryStatus
  ): Promise<ServiceResponse> {
    return this.updateSubscriptionEntry(userId, entryId, { status })
  }

  /**
   * Mark expired entries
   */
  static async markExpiredEntries(userId: string): Promise<string[]> {
    try {
      const entries = await this.getUserSubscriptionEntries(userId)
      const expiredEntryIds: string[] = []

      for (const entry of entries) {
        if (entry.status !== "expired" && isEntryExpired(entry)) {
          await this.updateEntryStatus(userId, entry.id, "expired")
          expiredEntryIds.push(entry.id)
        }
      }

      return expiredEntryIds
    } catch (error) {
      console.error("Error marking expired entries:", error)
      return []
    }
  }

  /**
   * Get aggregated subscription state for a user
   */
  static async getAggregatedSubscriptionState(userId: string): Promise<AggregatedSubscriptionState> {
    try {
      // Mark expired entries first
      await this.markExpiredEntries(userId)
      
      // Get all entries
      const allEntries = await this.getUserSubscriptionEntries(userId)
      
      // Get the active entry with highest precedence
      const activeEntry = getActiveEntry(allEntries)
      
      // Determine subscription state
      const isSubscribed = activeEntry !== null && activeEntry.subscriptionPlan !== "free"
      const subscriptionPlan = activeEntry?.subscriptionPlan || "free"
      const subscriptionStatus = activeEntry?.stripeData?.subscriptionStatus || null
      const currentPeriodEnd = activeEntry?.stripeData?.currentPeriodEnd || 
                              activeEntry?.endDate || null

      // Calculate perk enhancements (placeholder - will be implemented with perk integration)
      const perkEnhancements = null

      return {
        isSubscribed,
        subscriptionPlan,
        subscriptionStatus,
        currentPeriodEnd,
        activeEntry,
        allEntries,
        perkEnhancements
      }
    } catch (error) {
      console.error("Error getting aggregated subscription state:", error)
      
      // Return default state on error
      return {
        isSubscribed: false,
        subscriptionPlan: "free",
        subscriptionStatus: null,
        currentPeriodEnd: null,
        activeEntry: null,
        allEntries: [],
        perkEnhancements: null
      }
    }
  }
}
