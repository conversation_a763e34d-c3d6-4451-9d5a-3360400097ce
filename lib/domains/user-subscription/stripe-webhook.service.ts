import Stripe from "stripe"
import { stripe } from "@/lib/server/stripe"
import { ServiceResponse } from "../base/base.types"
import { 
  StripeSubscriptionData, 
  createStripeSubscriptionEntry,
  SubscriptionPlan 
} from "./user-subscription.types"
import { UserSubscriptionEntryService } from "./user-subscription-entry.service"
import { SubscriptionPrecedenceService } from "./subscription-precedence.service"
import { StripeSubscriptionService } from "./stripe-subscription.service"

/**
 * Service for handling Stripe webhook events with multi-entry subscription system
 */
export class StripeWebhookService {
  
  /**
   * Handle checkout session completed event
   */
  static async handleCheckoutSessionCompleted(
    session: Stripe.Checkout.Session,
    userId?: string
  ): Promise<ServiceResponse> {
    try {
      const customerId = session.customer as string
      const subscriptionId = session.subscription as string
      
      console.log(`Checkout session completed: ${session.id}`)
      console.log(`Customer ID: ${customerId}, Subscription ID: ${subscriptionId}`)

      // Get userId from metadata or find by customer ID
      let targetUserId = userId || session.metadata?.userId
      if (!targetUserId) {
        // TODO: Implement customer ID to user ID lookup
        console.error(`Could not find user for customer ID ${customerId}`)
        return { success: false, error: new Error("User not found") }
      }

      // Get subscription details from Stripe
      const subscription = await stripe.subscriptions.retrieve(subscriptionId)
      const planInterval = subscription.items.data[0].price?.recurring?.interval
      const subscriptionPlan: SubscriptionPlan = planInterval === "year" ? "yearly" : "monthly"

      // Create Stripe subscription data
      const stripeData: StripeSubscriptionData = {
        customerId,
        subscriptionId,
        subscriptionStatus: subscription.status as any,
        currentPeriodEnd: subscription.current_period_end
      }

      // Add userId to subscription metadata if not present
      if (!subscription.metadata?.userId) {
        console.log(`Adding userId ${targetUserId} to subscription metadata`)
        await stripe.subscriptions.update(subscriptionId, {
          metadata: { userId: targetUserId },
        })
      }

      // Check if user already has a Stripe subscription entry
      const existingStripeEntries = await UserSubscriptionEntryService.getEntriesBySource(
        targetUserId, 
        "stripe"
      )

      if (existingStripeEntries.length > 0) {
        // Update existing entry
        const existingEntry = existingStripeEntries[0]
        await UserSubscriptionEntryService.updateSubscriptionEntry(
          targetUserId,
          existingEntry.id,
          { 
            stripeData,
            subscriptionPlan,
            status: "applied"
          }
        )
        console.log(`Updated existing Stripe subscription entry for user ${targetUserId}`)
      } else {
        // Create new subscription entry
        const entryData = createStripeSubscriptionEntry(targetUserId, stripeData, subscriptionPlan)
        const result = await SubscriptionPrecedenceService.addSubscriptionWithPrecedence(entryData)
        
        if (!result.success) {
          console.error(`Failed to create subscription entry for user ${targetUserId}`)
          return result
        }
        console.log(`Created new Stripe subscription entry for user ${targetUserId}`)
      }

      // Recalculate precedence
      await SubscriptionPrecedenceService.recalculatePrecedence(targetUserId)

      return { success: true }
    } catch (error) {
      console.error("Error handling checkout session completed:", error)
      return { success: false, error }
    }
  }

  /**
   * Handle subscription updated event
   */
  static async handleSubscriptionUpdated(
    subscription: Stripe.Subscription,
    userId?: string
  ): Promise<ServiceResponse> {
    try {
      const subscriptionId = subscription.id
      const customerId = subscription.customer as string
      const status = subscription.status
      const planInterval = subscription.items.data[0].price?.recurring?.interval
      
      console.log(`Subscription updated: ${subscriptionId}, Status: ${status}`)

      // Get userId from metadata or parameter
      let targetUserId = userId || subscription.metadata?.userId
      if (!targetUserId) {
        console.error(`Could not find user for subscription ${subscriptionId}`)
        return { success: false, error: new Error("User not found") }
      }

      // Update subscription data
      const stripeData: StripeSubscriptionData = {
        customerId,
        subscriptionId,
        subscriptionStatus: status as any,
        currentPeriodEnd: subscription.current_period_end
      }

      const subscriptionPlan: SubscriptionPlan = planInterval === "year" ? "yearly" : "monthly"

      // Find existing Stripe entry
      const stripeEntries = await UserSubscriptionEntryService.getEntriesBySource(targetUserId, "stripe")
      const entry = stripeEntries.find(e => e.stripeData?.subscriptionId === subscriptionId)

      if (!entry) {
        console.error(`No subscription entry found for subscription ${subscriptionId}`)
        return { success: false, error: new Error("Subscription entry not found") }
      }

      // Update the entry
      await UserSubscriptionEntryService.updateSubscriptionEntry(
        targetUserId,
        entry.id,
        { 
          stripeData,
          subscriptionPlan
        }
      )

      // Handle status-specific logic
      if (status === "canceled") {
        // Remove the entry if subscription is cancelled
        await SubscriptionPrecedenceService.removeSubscriptionWithPrecedence(targetUserId, entry.id)
        console.log(`Removed cancelled subscription entry for user ${targetUserId}`)
      } else {
        // Recalculate precedence for other status changes
        await SubscriptionPrecedenceService.recalculatePrecedence(targetUserId)
      }

      return { success: true }
    } catch (error) {
      console.error("Error handling subscription updated:", error)
      return { success: false, error }
    }
  }

  /**
   * Handle subscription deleted event
   */
  static async handleSubscriptionDeleted(
    subscription: Stripe.Subscription,
    userId?: string
  ): Promise<ServiceResponse> {
    try {
      const subscriptionId = subscription.id
      
      console.log(`Subscription deleted: ${subscriptionId}`)

      // Get userId from metadata or parameter
      let targetUserId = userId || subscription.metadata?.userId
      if (!targetUserId) {
        console.error(`Could not find user for subscription ${subscriptionId}`)
        return { success: false, error: new Error("User not found") }
      }

      // Find and remove the subscription entry
      const stripeEntries = await UserSubscriptionEntryService.getEntriesBySource(targetUserId, "stripe")
      const entry = stripeEntries.find(e => e.stripeData?.subscriptionId === subscriptionId)

      if (entry) {
        await SubscriptionPrecedenceService.removeSubscriptionWithPrecedence(targetUserId, entry.id)
        console.log(`Removed deleted subscription entry for user ${targetUserId}`)
      }

      return { success: true }
    } catch (error) {
      console.error("Error handling subscription deleted:", error)
      return { success: false, error }
    }
  }

  /**
   * Handle invoice payment succeeded event
   */
  static async handleInvoicePaymentSucceeded(
    invoice: Stripe.Invoice,
    userId?: string
  ): Promise<ServiceResponse> {
    try {
      const subscriptionId = invoice.subscription as string
      
      if (!subscriptionId) {
        // Not a subscription invoice
        return { success: true }
      }

      console.log(`Invoice payment succeeded for subscription: ${subscriptionId}`)

      // Get userId from invoice metadata or parameter
      let targetUserId = userId || invoice.metadata?.userId
      if (!targetUserId && invoice.subscription) {
        // Try to get userId from subscription metadata
        const subscription = await stripe.subscriptions.retrieve(subscriptionId)
        targetUserId = subscription.metadata?.userId
      }

      if (!targetUserId) {
        console.error(`Could not find user for invoice ${invoice.id}`)
        return { success: false, error: new Error("User not found") }
      }

      // Sync subscription status to ensure it's up to date
      await StripeSubscriptionService.syncStripeSubscriptionStatus(targetUserId, subscriptionId)

      return { success: true }
    } catch (error) {
      console.error("Error handling invoice payment succeeded:", error)
      return { success: false, error }
    }
  }

  /**
   * Handle invoice payment failed event
   */
  static async handleInvoicePaymentFailed(
    invoice: Stripe.Invoice,
    userId?: string
  ): Promise<ServiceResponse> {
    try {
      const subscriptionId = invoice.subscription as string
      
      if (!subscriptionId) {
        // Not a subscription invoice
        return { success: true }
      }

      console.log(`Invoice payment failed for subscription: ${subscriptionId}`)

      // Get userId from invoice metadata or parameter
      let targetUserId = userId || invoice.metadata?.userId
      if (!targetUserId && invoice.subscription) {
        // Try to get userId from subscription metadata
        const subscription = await stripe.subscriptions.retrieve(subscriptionId)
        targetUserId = subscription.metadata?.userId
      }

      if (!targetUserId) {
        console.error(`Could not find user for invoice ${invoice.id}`)
        return { success: false, error: new Error("User not found") }
      }

      // Sync subscription status - might be past_due
      await StripeSubscriptionService.syncStripeSubscriptionStatus(targetUserId, subscriptionId)

      return { success: true }
    } catch (error) {
      console.error("Error handling invoice payment failed:", error)
      return { success: false, error }
    }
  }

  /**
   * Main webhook event handler
   */
  static async handleWebhookEvent(
    event: Stripe.Event,
    userId?: string
  ): Promise<ServiceResponse> {
    try {
      console.log(`Processing webhook event: ${event.type}`)

      switch (event.type) {
        case "checkout.session.completed":
          return await this.handleCheckoutSessionCompleted(
            event.data.object as Stripe.Checkout.Session,
            userId
          )

        case "customer.subscription.updated":
          return await this.handleSubscriptionUpdated(
            event.data.object as Stripe.Subscription,
            userId
          )

        case "customer.subscription.deleted":
          return await this.handleSubscriptionDeleted(
            event.data.object as Stripe.Subscription,
            userId
          )

        case "invoice.payment_succeeded":
          return await this.handleInvoicePaymentSucceeded(
            event.data.object as Stripe.Invoice,
            userId
          )

        case "invoice.payment_failed":
          return await this.handleInvoicePaymentFailed(
            event.data.object as Stripe.Invoice,
            userId
          )

        default:
          console.log(`Unhandled webhook event type: ${event.type}`)
          return { success: true } // Don't fail for unhandled events
      }
    } catch (error) {
      console.error("Error handling webhook event:", error)
      return { success: false, error }
    }
  }
}
