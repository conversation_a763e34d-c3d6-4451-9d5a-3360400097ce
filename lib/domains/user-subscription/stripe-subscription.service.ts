import { stripe } from "@/lib/server/stripe"
import { ServiceResponse } from "../base/base.types"
import { UserSubscriptionEntryService } from "./user-subscription-entry.service"
import { SubscriptionPrecedenceService } from "./subscription-precedence.service"
import { StripeSubscriptionData } from "./user-subscription.types"

/**
 * Service for managing Stripe subscription operations with multi-entry system
 */
export class StripeSubscriptionService {
  
  /**
   * Pause a Stripe subscription
   * Note: Stripe doesn't have a native "pause" feature, so we use pause_collection
   */
  static async pauseStripeSubscription(
    userId: string,
    entryId: string
  ): Promise<ServiceResponse> {
    try {
      // Get the subscription entry
      const entry = await UserSubscriptionEntryService.getSubscriptionEntry(userId, entryId)
      
      if (!entry || !entry.stripeData) {
        return { success: false, error: new Error("Stripe subscription entry not found") }
      }

      const { subscriptionId } = entry.stripeData

      // Pause collection on the Stripe subscription
      await stripe.subscriptions.update(subscriptionId, {
        pause_collection: {
          behavior: "void", // Don't charge during pause
        },
        metadata: {
          pausedBySystem: "true",
          pausedAt: new Date().toISOString(),
          userId: userId,
        }
      })

      // Update local entry status
      await UserSubscriptionEntryService.updateEntryStatus(userId, entryId, "paused")

      console.log(`Paused Stripe subscription ${subscriptionId} for user ${userId}`)
      return { success: true }
    } catch (error) {
      console.error("Error pausing Stripe subscription:", error)
      return { success: false, error }
    }
  }

  /**
   * Resume a Stripe subscription
   */
  static async resumeStripeSubscription(
    userId: string,
    entryId: string
  ): Promise<ServiceResponse> {
    try {
      // Get the subscription entry
      const entry = await UserSubscriptionEntryService.getSubscriptionEntry(userId, entryId)
      
      if (!entry || !entry.stripeData) {
        return { success: false, error: new Error("Stripe subscription entry not found") }
      }

      const { subscriptionId } = entry.stripeData

      // Resume collection on the Stripe subscription
      await stripe.subscriptions.update(subscriptionId, {
        pause_collection: null, // Remove pause
        metadata: {
          pausedBySystem: "false",
          resumedAt: new Date().toISOString(),
          userId: userId,
        }
      })

      // Update local entry status
      await UserSubscriptionEntryService.updateEntryStatus(userId, entryId, "applied")

      console.log(`Resumed Stripe subscription ${subscriptionId} for user ${userId}`)
      return { success: true }
    } catch (error) {
      console.error("Error resuming Stripe subscription:", error)
      return { success: false, error }
    }
  }

  /**
   * Cancel a Stripe subscription and remove entry
   */
  static async cancelStripeSubscription(
    userId: string,
    entryId: string
  ): Promise<ServiceResponse> {
    try {
      // Get the subscription entry
      const entry = await UserSubscriptionEntryService.getSubscriptionEntry(userId, entryId)
      
      if (!entry || !entry.stripeData) {
        return { success: false, error: new Error("Stripe subscription entry not found") }
      }

      const { subscriptionId } = entry.stripeData

      // Cancel the Stripe subscription
      await stripe.subscriptions.cancel(subscriptionId, {
        prorate: true, // Prorate the cancellation
      })

      // Remove the subscription entry
      const removeResult = await SubscriptionPrecedenceService.removeSubscriptionWithPrecedence(userId, entryId)

      console.log(`Cancelled Stripe subscription ${subscriptionId} for user ${userId}`)
      return removeResult
    } catch (error) {
      console.error("Error cancelling Stripe subscription:", error)
      return { success: false, error }
    }
  }

  /**
   * Sync Stripe subscription status with local entry
   */
  static async syncStripeSubscriptionStatus(
    userId: string,
    subscriptionId: string
  ): Promise<ServiceResponse> {
    try {
      // Get Stripe subscription details
      const stripeSubscription = await stripe.subscriptions.retrieve(subscriptionId)
      
      // Get local entry
      const stripeEntries = await UserSubscriptionEntryService.getEntriesBySource(userId, "stripe")
      const entry = stripeEntries.find(e => e.stripeData?.subscriptionId === subscriptionId)
      
      if (!entry) {
        return { success: false, error: new Error("Local subscription entry not found") }
      }

      // Update local entry with Stripe data
      const updatedStripeData: StripeSubscriptionData = {
        customerId: stripeSubscription.customer as string,
        subscriptionId: stripeSubscription.id,
        subscriptionStatus: stripeSubscription.status as any,
        currentPeriodEnd: stripeSubscription.current_period_end
      }

      await UserSubscriptionEntryService.updateSubscriptionEntry(userId, entry.id, {
        stripeData: updatedStripeData
      })

      // Handle status changes
      if (stripeSubscription.status === "canceled") {
        // Remove entry if cancelled in Stripe
        await SubscriptionPrecedenceService.removeSubscriptionWithPrecedence(userId, entry.id)
      } else {
        // Recalculate precedence
        await SubscriptionPrecedenceService.recalculatePrecedence(userId)
      }

      return { success: true }
    } catch (error) {
      console.error("Error syncing Stripe subscription status:", error)
      return { success: false, error }
    }
  }

  /**
   * Check if a Stripe subscription is paused by the system
   */
  static async isSubscriptionPausedBySystem(subscriptionId: string): Promise<boolean> {
    try {
      const stripeSubscription = await stripe.subscriptions.retrieve(subscriptionId)
      return stripeSubscription.metadata?.pausedBySystem === "true"
    } catch (error) {
      console.error("Error checking if subscription is paused:", error)
      return false
    }
  }

  /**
   * Get Stripe subscription details
   */
  static async getStripeSubscriptionDetails(subscriptionId: string) {
    try {
      const subscription = await stripe.subscriptions.retrieve(subscriptionId, {
        expand: ['customer', 'items.data.price']
      })
      
      return {
        id: subscription.id,
        status: subscription.status,
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
        cancelAtPeriodEnd: subscription.cancel_at_period_end,
        pauseCollection: subscription.pause_collection,
        metadata: subscription.metadata,
        customer: subscription.customer,
        items: subscription.items.data.map(item => ({
          id: item.id,
          priceId: item.price.id,
          quantity: item.quantity,
          amount: item.price.unit_amount,
          interval: item.price.recurring?.interval,
        }))
      }
    } catch (error) {
      console.error("Error getting Stripe subscription details:", error)
      return null
    }
  }

  /**
   * Handle Stripe webhook events for subscription changes
   */
  static async handleStripeWebhookEvent(
    eventType: string,
    subscriptionData: any,
    userId?: string
  ): Promise<ServiceResponse> {
    try {
      const subscriptionId = subscriptionData.id
      const customerId = subscriptionData.customer

      // Find user by customer ID if userId not provided
      let targetUserId = userId
      if (!targetUserId) {
        // TODO: Implement customer ID to user ID lookup
        // This would require a separate service or database query
        console.warn("User ID not provided for webhook event, skipping")
        return { success: false, error: new Error("User ID not found") }
      }

      switch (eventType) {
        case "customer.subscription.created":
          // Handle new subscription creation
          await this.handleSubscriptionCreated(targetUserId, subscriptionData)
          break

        case "customer.subscription.updated":
          // Handle subscription updates
          await this.handleSubscriptionUpdated(targetUserId, subscriptionData)
          break

        case "customer.subscription.deleted":
          // Handle subscription cancellation
          await this.handleSubscriptionDeleted(targetUserId, subscriptionId)
          break

        case "invoice.payment_succeeded":
          // Handle successful payment
          await this.handlePaymentSucceeded(targetUserId, subscriptionId)
          break

        case "invoice.payment_failed":
          // Handle failed payment
          await this.handlePaymentFailed(targetUserId, subscriptionId)
          break

        default:
          console.log(`Unhandled webhook event type: ${eventType}`)
      }

      return { success: true }
    } catch (error) {
      console.error("Error handling Stripe webhook event:", error)
      return { success: false, error }
    }
  }

  /**
   * Handle subscription created webhook
   */
  private static async handleSubscriptionCreated(userId: string, subscriptionData: any): Promise<void> {
    // This would typically be handled by the checkout completion flow
    // But we can sync the status here as well
    await this.syncStripeSubscriptionStatus(userId, subscriptionData.id)
  }

  /**
   * Handle subscription updated webhook
   */
  private static async handleSubscriptionUpdated(userId: string, subscriptionData: any): Promise<void> {
    await this.syncStripeSubscriptionStatus(userId, subscriptionData.id)
  }

  /**
   * Handle subscription deleted webhook
   */
  private static async handleSubscriptionDeleted(userId: string, subscriptionId: string): Promise<void> {
    // Find and remove the subscription entry
    const stripeEntries = await UserSubscriptionEntryService.getEntriesBySource(userId, "stripe")
    const entry = stripeEntries.find(e => e.stripeData?.subscriptionId === subscriptionId)
    
    if (entry) {
      await SubscriptionPrecedenceService.removeSubscriptionWithPrecedence(userId, entry.id)
    }
  }

  /**
   * Handle payment succeeded webhook
   */
  private static async handlePaymentSucceeded(userId: string, subscriptionId: string): Promise<void> {
    // Sync subscription status and ensure it's active
    await this.syncStripeSubscriptionStatus(userId, subscriptionId)
  }

  /**
   * Handle payment failed webhook
   */
  private static async handlePaymentFailed(userId: string, subscriptionId: string): Promise<void> {
    // Sync subscription status - might be past_due
    await this.syncStripeSubscriptionStatus(userId, subscriptionId)
  }
}
