import { 
  collection, 
  doc, 
  getDocs, 
  getDoc,
  setDoc,
  serverTimestamp
} from "firebase/firestore"
import { db } from "@/lib/firebase"
import { ServiceResponse } from "../base/base.types"
import { UserSubscription, createStripeSubscriptionEntry } from "./user-subscription.types"
import { EnhancedSubscriptionService } from "./enhanced-subscription.service"

/**
 * Simple migration service that enhances existing subscriptions without breaking changes
 */
export class SimpleMigrationService {
  private static readonly COLLECTION = "userSubscriptions"

  /**
   * Migrate a single user's subscription to enhanced format
   * This creates entries subcollection while keeping main document intact
   */
  static async migrateUserToEnhanced(userId: string): Promise<ServiceResponse<{
    migrated: boolean
    reason?: string
    entryId?: string
  }>> {
    try {
      // Get existing subscription
      const subscriptionDoc = await getDoc(doc(db, this.COLLECTION, userId))
      
      if (!subscriptionDoc.exists()) {
        return {
          success: true,
          data: { migrated: false, reason: "No existing subscription found" }
        }
      }

      const subscriptionData = subscriptionDoc.data() as UserSubscription

      // Check if already has entries (already migrated)
      const existingEntries = await EnhancedSubscriptionService.getSubscriptionEntries(userId)
      if (existingEntries.length > 0) {
        return {
          success: true,
          data: { migrated: false, reason: "Already has subscription entries" }
        }
      }

      // Skip if it's a free subscription with no Stripe data
      if (subscriptionData.subscriptionPlan === "free" && 
          !subscriptionData.stripeCustomerId && 
          !subscriptionData.subscriptionId) {
        return {
          success: true,
          data: { migrated: false, reason: "Free subscription with no data to migrate" }
        }
      }

      // Create Stripe entry if there's Stripe data
      if (subscriptionData.stripeCustomerId && subscriptionData.subscriptionId) {
        const stripeData = {
          customerId: subscriptionData.stripeCustomerId,
          subscriptionId: subscriptionData.subscriptionId,
          subscriptionStatus: subscriptionData.subscriptionStatus || "active",
          currentPeriodEnd: subscriptionData.subscriptionCurrentPeriodEnd || Date.now()
        }

        const result = await EnhancedSubscriptionService.addStripeSubscription(
          userId,
          stripeData,
          subscriptionData.subscriptionPlan as "monthly" | "yearly"
        )

        if (result.success) {
          return {
            success: true,
            data: { migrated: true, entryId: "stripe-entry" }
          }
        } else {
          return {
            success: false,
            error: result.error,
            data: { migrated: false, reason: "Failed to create Stripe entry" }
          }
        }
      }

      return {
        success: true,
        data: { migrated: false, reason: "No valid subscription data to migrate" }
      }
    } catch (error) {
      console.error(`Error migrating user subscription for ${userId}:`, error)
      return { success: false, error }
    }
  }

  /**
   * Migrate all users to enhanced format (safe, non-destructive)
   */
  static async migrateAllToEnhanced(): Promise<ServiceResponse<{
    totalProcessed: number
    successfulMigrations: number
    skippedMigrations: number
    errors: string[]
  }>> {
    try {
      console.log("Starting safe migration to enhanced subscription format...")
      
      const subscriptionsSnapshot = await getDocs(collection(db, this.COLLECTION))
      const errors: string[] = []
      let totalProcessed = 0
      let successfulMigrations = 0
      let skippedMigrations = 0

      for (const subscriptionDoc of subscriptionsSnapshot.docs) {
        const userId = subscriptionDoc.id
        totalProcessed++

        try {
          const migrationResult = await this.migrateUserToEnhanced(userId)
          
          if (migrationResult.success) {
            if (migrationResult.data?.migrated) {
              successfulMigrations++
              console.log(`✅ Migrated subscription for user ${userId}`)
            } else {
              skippedMigrations++
              console.log(`⏭️ Skipped migration for user ${userId}: ${migrationResult.data?.reason}`)
            }
          } else {
            const errorMsg = `Failed to migrate user ${userId}: ${migrationResult.error?.message}`
            errors.push(errorMsg)
            console.error(`❌ ${errorMsg}`)
          }
        } catch (error) {
          const errorMsg = `Exception during migration for user ${userId}: ${error}`
          errors.push(errorMsg)
          console.error(`💥 ${errorMsg}`)
        }

        // Small delay to avoid overwhelming the database
        if (totalProcessed % 10 === 0) {
          await new Promise(resolve => setTimeout(resolve, 100))
        }
      }

      console.log(`Migration completed: ${successfulMigrations} migrated, ${skippedMigrations} skipped, ${errors.length} errors`)

      return {
        success: true,
        data: {
          totalProcessed,
          successfulMigrations,
          skippedMigrations,
          errors
        }
      }
    } catch (error) {
      console.error("Error during migration:", error)
      return { success: false, error }
    }
  }

  /**
   * Check if a user has been migrated to enhanced format
   */
  static async isUserMigrated(userId: string): Promise<boolean> {
    try {
      const entries = await EnhancedSubscriptionService.getSubscriptionEntries(userId)
      return entries.length > 0
    } catch (error) {
      console.error("Error checking migration status:", error)
      return false
    }
  }

  /**
   * Get migration status for all users
   */
  static async getMigrationStatus(): Promise<{
    totalUsers: number
    migratedUsers: number
    unmigrated: string[]
    migrationRate: number
  }> {
    try {
      const subscriptionsSnapshot = await getDocs(collection(db, this.COLLECTION))
      const totalUsers = subscriptionsSnapshot.size
      let migratedUsers = 0
      const unmigrated: string[] = []

      for (const subscriptionDoc of subscriptionsSnapshot.docs) {
        const userId = subscriptionDoc.id
        const isMigrated = await this.isUserMigrated(userId)
        
        if (isMigrated) {
          migratedUsers++
        } else {
          unmigrated.push(userId)
        }
      }

      const migrationRate = totalUsers > 0 ? (migratedUsers / totalUsers) * 100 : 0

      return {
        totalUsers,
        migratedUsers,
        unmigrated,
        migrationRate
      }
    } catch (error) {
      console.error("Error getting migration status:", error)
      return {
        totalUsers: 0,
        migratedUsers: 0,
        unmigrated: [],
        migrationRate: 0
      }
    }
  }

  /**
   * Validate that enhanced subscriptions work correctly
   */
  static async validateEnhancedSystem(): Promise<ServiceResponse<{
    isValid: boolean
    issues: string[]
    checkedUsers: number
  }>> {
    try {
      console.log("Validating enhanced subscription system...")
      
      const issues: string[] = []
      let checkedUsers = 0
      
      // Get a sample of users to validate
      const subscriptionsSnapshot = await getDocs(collection(db, this.COLLECTION))
      const sampleSize = Math.min(20, subscriptionsSnapshot.size)
      
      for (let i = 0; i < sampleSize; i++) {
        const subscriptionDoc = subscriptionsSnapshot.docs[i]
        const userId = subscriptionDoc.id
        checkedUsers++

        try {
          // Test enhanced subscription retrieval
          const enhancedSubscription = await EnhancedSubscriptionService.getEnhancedUserSubscription(userId)
          const entries = await EnhancedSubscriptionService.getSubscriptionEntries(userId)
          const summary = await EnhancedSubscriptionService.getSubscriptionSummary(userId)

          // Basic validation checks
          if (enhancedSubscription && entries.length > 0) {
            if (enhancedSubscription.totalEntries !== entries.length) {
              issues.push(`User ${userId}: totalEntries mismatch`)
            }
            
            if (enhancedSubscription.hasMultipleEntries !== (entries.length > 1)) {
              issues.push(`User ${userId}: hasMultipleEntries flag incorrect`)
            }
          }

          // Validate summary consistency
          if (summary.totalEntries !== entries.length) {
            issues.push(`User ${userId}: summary totalEntries mismatch`)
          }

        } catch (error) {
          issues.push(`User ${userId}: validation error - ${error}`)
        }
      }

      const isValid = issues.length === 0

      console.log(`Validation completed: ${isValid ? "PASSED" : "FAILED"} with ${issues.length} issues`)

      return {
        success: true,
        data: {
          isValid,
          issues,
          checkedUsers
        }
      }
    } catch (error) {
      console.error("Error validating enhanced system:", error)
      return { success: false, error }
    }
  }

  /**
   * Create a simple cron job to process expired entries
   */
  static async processAllExpiredEntries(): Promise<ServiceResponse<{
    processedUsers: number
    totalExpired: number
    errors: string[]
  }>> {
    try {
      console.log("Processing expired entries for all users...")
      
      const subscriptionsSnapshot = await getDocs(collection(db, this.COLLECTION))
      const errors: string[] = []
      let processedUsers = 0
      let totalExpired = 0

      for (const subscriptionDoc of subscriptionsSnapshot.docs) {
        const userId = subscriptionDoc.id
        
        try {
          const result = await EnhancedSubscriptionService.processExpiredEntries(userId)
          
          if (result.success) {
            processedUsers++
            totalExpired += result.data!.expiredCount
          } else {
            errors.push(`Failed to process user ${userId}: ${result.error?.message}`)
          }
        } catch (error) {
          errors.push(`Exception processing user ${userId}: ${error}`)
        }
      }

      console.log(`Processed ${processedUsers} users, expired ${totalExpired} entries`)

      return {
        success: true,
        data: {
          processedUsers,
          totalExpired,
          errors
        }
      }
    } catch (error) {
      console.error("Error processing expired entries:", error)
      return { success: false, error }
    }
  }
}
