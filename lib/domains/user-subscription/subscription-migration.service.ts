import { 
  collection, 
  doc, 
  getDocs, 
  getDoc,
  setDoc,
  updateDoc,
  runTransaction,
  serverTimestamp,
  Timestamp
} from "firebase/firestore"
import { db } from "@/lib/firebase"
import { ServiceResponse } from "../base/base.types"
import {
  UserSubscription,
  UserSubscriptionEntry,
  createStripeSubscriptionEntry,
  StripeSubscriptionData,
  SubscriptionPlan
} from "./user-subscription.types"
import { UserSubscriptionEntryService } from "./user-subscription-entry.service"
import { SubscriptionPrecedenceService } from "./subscription-precedence.service"

/**
 * Service for migrating from single subscription to multi-entry system
 */
export class SubscriptionMigrationService {
  private static readonly LEGACY_COLLECTION = "userSubscriptions"
  private static readonly BACKUP_COLLECTION = "userSubscriptionsBackup"
  private static readonly MIGRATION_STATUS_COLLECTION = "migrationStatus"

  /**
   * Backup existing subscription data before migration
   */
  static async backupExistingSubscriptions(): Promise<ServiceResponse<{
    backedUpCount: number
    errors: string[]
  }>> {
    try {
      console.log("Starting subscription data backup...")
      
      const subscriptionsSnapshot = await getDocs(collection(db, this.LEGACY_COLLECTION))
      const errors: string[] = []
      let backedUpCount = 0

      for (const subscriptionDoc of subscriptionsSnapshot.docs) {
        try {
          const subscriptionData = subscriptionDoc.data()
          
          // Create backup document
          await setDoc(doc(db, this.BACKUP_COLLECTION, subscriptionDoc.id), {
            ...subscriptionData,
            backedUpAt: serverTimestamp(),
            originalId: subscriptionDoc.id
          })
          
          backedUpCount++
        } catch (error) {
          const errorMsg = `Failed to backup subscription ${subscriptionDoc.id}: ${error}`
          console.error(errorMsg)
          errors.push(errorMsg)
        }
      }

      console.log(`Backup completed: ${backedUpCount} subscriptions backed up`)
      
      return {
        success: true,
        data: { backedUpCount, errors }
      }
    } catch (error) {
      console.error("Error backing up subscriptions:", error)
      return { success: false, error }
    }
  }

  /**
   * Migrate a single user's subscription to multi-entry system
   */
  static async migrateUserSubscription(userId: string): Promise<ServiceResponse<{
    migrated: boolean
    entryId?: string
    reason?: string
  }>> {
    try {
      // Get existing subscription
      const subscriptionDoc = await getDoc(doc(db, this.LEGACY_COLLECTION, userId))
      
      if (!subscriptionDoc.exists()) {
        return {
          success: true,
          data: { migrated: false, reason: "No existing subscription found" }
        }
      }

      const subscriptionData = subscriptionDoc.data() as UserSubscription

      // Skip if it's already a free subscription with no Stripe data
      if (subscriptionData.subscriptionPlan === "free" && 
          !subscriptionData.stripeCustomerId && 
          !subscriptionData.subscriptionId) {
        return {
          success: true,
          data: { migrated: false, reason: "Free subscription with no Stripe data" }
        }
      }

      // Check if user already has entries in the new system
      const existingEntries = await UserSubscriptionEntryService.getUserSubscriptionEntries(userId)
      if (existingEntries.length > 0) {
        return {
          success: true,
          data: { migrated: false, reason: "User already has subscription entries" }
        }
      }

      // Create Stripe subscription entry if there's Stripe data
      if (subscriptionData.stripeCustomerId && subscriptionData.subscriptionId) {
        const stripeData: StripeSubscriptionData = {
          customerId: subscriptionData.stripeCustomerId,
          subscriptionId: subscriptionData.subscriptionId,
          subscriptionStatus: subscriptionData.subscriptionStatus || "active",
          currentPeriodEnd: subscriptionData.subscriptionCurrentPeriodEnd || Date.now()
        }

        const entryData = createStripeSubscriptionEntry(
          userId,
          stripeData,
          subscriptionData.subscriptionPlan
        )

        const createResult = await UserSubscriptionEntryService.createSubscriptionEntry(entryData)
        
        if (!createResult.success) {
          return {
            success: false,
            error: createResult.error,
            data: { migrated: false, reason: "Failed to create subscription entry" }
          }
        }

        // Mark migration as completed for this user
        await this.markUserMigrationCompleted(userId, createResult.id!)

        return {
          success: true,
          data: { migrated: true, entryId: createResult.id }
        }
      }

      return {
        success: true,
        data: { migrated: false, reason: "No valid subscription data to migrate" }
      }
    } catch (error) {
      console.error(`Error migrating user subscription for ${userId}:`, error)
      return { success: false, error }
    }
  }

  /**
   * Migrate all user subscriptions to multi-entry system
   */
  static async migrateAllSubscriptions(): Promise<ServiceResponse<{
    totalProcessed: number
    successfulMigrations: number
    skippedMigrations: number
    errors: string[]
  }>> {
    try {
      console.log("Starting full subscription migration...")
      
      // First, backup existing data
      const backupResult = await this.backupExistingSubscriptions()
      if (!backupResult.success) {
        return { success: false, error: backupResult.error }
      }

      const subscriptionsSnapshot = await getDocs(collection(db, this.LEGACY_COLLECTION))
      const errors: string[] = []
      let totalProcessed = 0
      let successfulMigrations = 0
      let skippedMigrations = 0

      for (const subscriptionDoc of subscriptionsSnapshot.docs) {
        const userId = subscriptionDoc.id
        totalProcessed++

        try {
          const migrationResult = await this.migrateUserSubscription(userId)
          
          if (migrationResult.success) {
            if (migrationResult.data?.migrated) {
              successfulMigrations++
              console.log(`✅ Migrated subscription for user ${userId}`)
            } else {
              skippedMigrations++
              console.log(`⏭️ Skipped migration for user ${userId}: ${migrationResult.data?.reason}`)
            }
          } else {
            const errorMsg = `Failed to migrate user ${userId}: ${migrationResult.error?.message}`
            errors.push(errorMsg)
            console.error(`❌ ${errorMsg}`)
          }
        } catch (error) {
          const errorMsg = `Exception during migration for user ${userId}: ${error}`
          errors.push(errorMsg)
          console.error(`💥 ${errorMsg}`)
        }

        // Add small delay to avoid overwhelming the database
        if (totalProcessed % 10 === 0) {
          await new Promise(resolve => setTimeout(resolve, 100))
        }
      }

      // Record migration completion
      await this.recordMigrationCompletion({
        totalProcessed,
        successfulMigrations,
        skippedMigrations,
        errorCount: errors.length,
        completedAt: new Date()
      })

      console.log(`Migration completed: ${successfulMigrations} migrated, ${skippedMigrations} skipped, ${errors.length} errors`)

      return {
        success: true,
        data: {
          totalProcessed,
          successfulMigrations,
          skippedMigrations,
          errors
        }
      }
    } catch (error) {
      console.error("Error during full migration:", error)
      return { success: false, error }
    }
  }

  /**
   * Validate migration results
   */
  static async validateMigration(): Promise<ServiceResponse<{
    isValid: boolean
    issues: string[]
    statistics: {
      legacySubscriptions: number
      newEntries: number
      migratedUsers: number
    }
  }>> {
    try {
      console.log("Starting migration validation...")
      
      const issues: string[] = []
      
      // Count legacy subscriptions
      const legacySnapshot = await getDocs(collection(db, this.LEGACY_COLLECTION))
      const legacySubscriptions = legacySnapshot.size

      // Count new entries across all users
      let newEntries = 0
      let migratedUsers = 0
      
      for (const legacyDoc of legacySnapshot.docs) {
        const userId = legacyDoc.id
        const legacyData = legacyDoc.data() as UserSubscription
        
        // Skip free subscriptions with no Stripe data
        if (legacyData.subscriptionPlan === "free" && 
            !legacyData.stripeCustomerId && 
            !legacyData.subscriptionId) {
          continue
        }

        const userEntries = await UserSubscriptionEntryService.getUserSubscriptionEntries(userId)
        newEntries += userEntries.length

        if (userEntries.length > 0) {
          migratedUsers++
          
          // Validate that Stripe data matches
          const stripeEntry = userEntries.find(entry => entry.source === "stripe")
          if (stripeEntry && legacyData.stripeCustomerId) {
            if (stripeEntry.stripeData?.customerId !== legacyData.stripeCustomerId) {
              issues.push(`User ${userId}: Stripe customer ID mismatch`)
            }
            if (stripeEntry.stripeData?.subscriptionId !== legacyData.subscriptionId) {
              issues.push(`User ${userId}: Stripe subscription ID mismatch`)
            }
            if (stripeEntry.subscriptionPlan !== legacyData.subscriptionPlan) {
              issues.push(`User ${userId}: Subscription plan mismatch`)
            }
          }
        } else if (legacyData.stripeCustomerId || legacyData.subscriptionId) {
          issues.push(`User ${userId}: Has Stripe data but no migrated entries`)
        }
      }

      const isValid = issues.length === 0

      console.log(`Validation completed: ${isValid ? "PASSED" : "FAILED"} with ${issues.length} issues`)

      return {
        success: true,
        data: {
          isValid,
          issues,
          statistics: {
            legacySubscriptions,
            newEntries,
            migratedUsers
          }
        }
      }
    } catch (error) {
      console.error("Error validating migration:", error)
      return { success: false, error }
    }
  }

  /**
   * Mark a user's migration as completed
   */
  private static async markUserMigrationCompleted(userId: string, entryId: string): Promise<void> {
    await setDoc(doc(db, this.MIGRATION_STATUS_COLLECTION, userId), {
      migrated: true,
      entryId,
      migratedAt: serverTimestamp()
    })
  }

  /**
   * Record overall migration completion
   */
  private static async recordMigrationCompletion(stats: {
    totalProcessed: number
    successfulMigrations: number
    skippedMigrations: number
    errorCount: number
    completedAt: Date
  }): Promise<void> {
    await setDoc(doc(db, this.MIGRATION_STATUS_COLLECTION, "_migration_summary"), {
      ...stats,
      completedAt: serverTimestamp()
    })
  }

  /**
   * Get migration status
   */
  static async getMigrationStatus(): Promise<{
    isCompleted: boolean
    summary?: any
    userStatuses: Array<{
      userId: string
      migrated: boolean
      entryId?: string
      migratedAt?: Date
    }>
  }> {
    try {
      // Get migration summary
      const summaryDoc = await getDoc(doc(db, this.MIGRATION_STATUS_COLLECTION, "_migration_summary"))
      const summary = summaryDoc.exists() ? summaryDoc.data() : null

      // Get user migration statuses
      const statusSnapshot = await getDocs(collection(db, this.MIGRATION_STATUS_COLLECTION))
      const userStatuses = statusSnapshot.docs
        .filter(doc => doc.id !== "_migration_summary")
        .map(doc => ({
          userId: doc.id,
          ...doc.data(),
          migratedAt: doc.data().migratedAt?.toDate()
        }))

      return {
        isCompleted: !!summary,
        summary,
        userStatuses
      }
    } catch (error) {
      console.error("Error getting migration status:", error)
      return {
        isCompleted: false,
        userStatuses: []
      }
    }
  }

  /**
   * Rollback migration for a specific user (emergency use only)
   */
  static async rollbackUserMigration(userId: string): Promise<ServiceResponse> {
    try {
      console.log(`Rolling back migration for user ${userId}`)

      // Get backup data
      const backupDoc = await getDoc(doc(db, this.BACKUP_COLLECTION, userId))
      if (!backupDoc.exists()) {
        return { success: false, error: new Error("No backup data found for user") }
      }

      // Remove all subscription entries
      const entries = await UserSubscriptionEntryService.getUserSubscriptionEntries(userId)
      for (const entry of entries) {
        await UserSubscriptionEntryService.deleteSubscriptionEntry(userId, entry.id)
      }

      // Restore original subscription data
      const backupData = backupDoc.data()
      const { backedUpAt, originalId, ...originalData } = backupData

      await setDoc(doc(db, this.LEGACY_COLLECTION, userId), {
        ...originalData,
        restoredAt: serverTimestamp()
      })

      // Remove migration status
      await setDoc(doc(db, this.MIGRATION_STATUS_COLLECTION, userId), {
        migrated: false,
        rolledBack: true,
        rolledBackAt: serverTimestamp()
      })

      console.log(`Successfully rolled back migration for user ${userId}`)
      return { success: true }
    } catch (error) {
      console.error(`Error rolling back migration for user ${userId}:`, error)
      return { success: false, error }
    }
  }

  /**
   * Clean up migration artifacts (run after successful migration and validation)
   */
  static async cleanupMigrationArtifacts(): Promise<ServiceResponse<{
    cleanedBackups: number
    cleanedStatuses: number
  }>> {
    try {
      console.log("Cleaning up migration artifacts...")

      // Clean up backup collection (keep for 30 days after migration)
      const migrationStatus = await this.getMigrationStatus()
      if (!migrationStatus.isCompleted) {
        return { success: false, error: new Error("Migration not completed, cannot cleanup") }
      }

      const migrationDate = migrationStatus.summary?.completedAt?.toDate()
      if (!migrationDate) {
        return { success: false, error: new Error("Migration completion date not found") }
      }

      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      if (migrationDate > thirtyDaysAgo) {
        return { success: false, error: new Error("Migration too recent, wait 30 days before cleanup") }
      }

      // This would be implemented with admin SDK for bulk deletion
      // For now, just return success
      console.log("Migration artifacts cleanup would be performed here")

      return {
        success: true,
        data: {
          cleanedBackups: 0,
          cleanedStatuses: 0
        }
      }
    } catch (error) {
      console.error("Error cleaning up migration artifacts:", error)
      return { success: false, error }
    }
  }
}
