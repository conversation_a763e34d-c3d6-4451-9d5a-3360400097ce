import { ServiceResponse } from "../base/base.types"
import { 
  UserPerk, 
  PerkService,
  PerkApplicationResult 
} from "../perk/perk.service"
import { 
  createPerkSubscriptionEntry,
  PerkSubscriptionData,
  SubscriptionPlan
} from "./user-subscription.types"
import { UserSubscriptionEntryService } from "./user-subscription-entry.service"
import { SubscriptionPrecedenceService } from "./subscription-precedence.service"
import { SubscriptionAggregationService } from "./subscription-aggregation.service"

/**
 * Service for integrating perk system with multi-entry subscription system
 */
export class PerkSubscriptionService {
  
  /**
   * Apply a subscription perk by creating a subscription entry
   */
  static async applySubscriptionPerk(
    userId: string,
    perkId: string
  ): Promise<PerkApplicationResult> {
    try {
      // Get the user perk
      const userPerks = await PerkService.getUserPerks(userId)
      const userPerk = userPerks.find(p => p.perkId === perkId)
      
      if (!userPerk) {
        return {
          success: false,
          error: "User perk not found"
        }
      }

      if (userPerk.status !== "unlocked") {
        return {
          success: false,
          error: "Perk is not in unlocked status"
        }
      }

      if (userPerk.perkDetails.perkType !== "subscription") {
        return {
          success: false,
          error: "Perk is not a subscription perk"
        }
      }

      // Check if perk is already applied
      const existingPerkEntries = await UserSubscriptionEntryService.getEntriesBySource(userId, "perk")
      const existingPerkEntry = existingPerkEntries.find(entry => 
        entry.perkData?.perkId === perkId
      )

      if (existingPerkEntry) {
        return {
          success: false,
          error: "Perk subscription already applied"
        }
      }

      // Get perk duration from perk value
      const perkValue = userPerk.perkDetails.perkValue
      const duration = perkValue.duration || 30 // Default to 30 days if not specified
      
      // Create perk subscription data
      const perkData: PerkSubscriptionData = {
        perkId,
        appliedAt: new Date() as any,
        duration
      }

      // Determine subscription plan (default to monthly for perk subscriptions)
      const subscriptionPlan: SubscriptionPlan = "monthly"

      // Create subscription entry
      const entryData = createPerkSubscriptionEntry(userId, perkData, subscriptionPlan)
      const createResult = await SubscriptionPrecedenceService.addSubscriptionWithPrecedence(entryData)

      if (!createResult.success) {
        return {
          success: false,
          error: createResult.error?.message || "Failed to create perk subscription entry"
        }
      }

      // Update perk status to applied
      await PerkService.updateUserPerk(userId, perkId, {
        status: "applied",
        appliedAt: new Date() as any
      })

      console.log(`Applied subscription perk ${perkId} for user ${userId}`)

      return {
        success: true,
        perkId,
        appliedAt: new Date(),
        expiresAt: new Date(Date.now() + duration * 24 * 60 * 60 * 1000)
      }
    } catch (error) {
      console.error("Error applying subscription perk:", error)
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error"
      }
    }
  }

  /**
   * Check for expired perk subscriptions and update their status
   */
  static async processExpiredPerkSubscriptions(userId: string): Promise<{
    expiredCount: number
    errors: string[]
  }> {
    try {
      const perkEntries = await UserSubscriptionEntryService.getEntriesBySource(userId, "perk")
      const errors: string[] = []
      let expiredCount = 0

      for (const entry of perkEntries) {
        if (entry.status !== "expired" && entry.endDate) {
          const endDate = entry.endDate instanceof Date ? entry.endDate : entry.endDate.toDate()
          const now = new Date()

          if (endDate <= now) {
            // Mark entry as expired
            const updateResult = await UserSubscriptionEntryService.updateEntryStatus(
              userId, 
              entry.id, 
              "expired"
            )

            if (updateResult.success) {
              expiredCount++
              
              // Update corresponding perk status
              if (entry.perkData?.perkId) {
                await PerkService.updateUserPerk(userId, entry.perkData.perkId, {
                  status: "expired"
                })
              }
            } else {
              errors.push(`Failed to expire perk subscription entry ${entry.id}`)
            }
          }
        }
      }

      // Recalculate precedence after expiring entries
      if (expiredCount > 0) {
        await SubscriptionPrecedenceService.recalculatePrecedence(userId)
      }

      return { expiredCount, errors }
    } catch (error) {
      console.error("Error processing expired perk subscriptions:", error)
      return { expiredCount: 0, errors: ["Failed to process expired perk subscriptions"] }
    }
  }

  /**
   * Get all active perk subscriptions for a user
   */
  static async getActivePerkSubscriptions(userId: string): Promise<Array<{
    entryId: string
    perkId: string
    perkName: string
    appliedAt: Date
    expiresAt: Date | null
    status: string
  }>> {
    try {
      const perkEntries = await UserSubscriptionEntryService.getEntriesBySource(userId, "perk")
      const activePerkSubscriptions = []

      for (const entry of perkEntries) {
        if (entry.perkData && entry.status !== "expired") {
          const appliedAt = entry.perkData.appliedAt instanceof Date 
            ? entry.perkData.appliedAt 
            : entry.perkData.appliedAt.toDate()
          
          const expiresAt = entry.endDate 
            ? (entry.endDate instanceof Date ? entry.endDate : entry.endDate.toDate())
            : null

          // Get perk details
          const userPerks = await PerkService.getUserPerks(userId)
          const userPerk = userPerks.find(p => p.perkId === entry.perkData!.perkId)
          const perkName = userPerk?.perkDetails.name || "Unknown Perk"

          activePerkSubscriptions.push({
            entryId: entry.id,
            perkId: entry.perkData.perkId,
            perkName,
            appliedAt,
            expiresAt,
            status: entry.status
          })
        }
      }

      return activePerkSubscriptions
    } catch (error) {
      console.error("Error getting active perk subscriptions:", error)
      return []
    }
  }

  /**
   * Calculate perk enhancements for subscription limits
   */
  static async calculatePerkEnhancements(userId: string): Promise<{
    additionalSquads: number
    additionalTripsPerSquad: number
    additionalDailyAI: number
    additionalWeeklyAI: number
    activePerkIds: string[]
  }> {
    try {
      const userPerks = await PerkService.getUserPerks(userId)
      const appliedPerks = userPerks.filter(perk => perk.status === "applied")

      let additionalSquads = 0
      let additionalTripsPerSquad = 0
      let additionalDailyAI = 0
      let additionalWeeklyAI = 0
      const activePerkIds: string[] = []

      for (const perk of appliedPerks) {
        activePerkIds.push(perk.perkId)

        // Add perk value enhancements
        const perkValue = perk.perkDetails.perkValue
        
        if (perkValue.maxSquads) {
          additionalSquads += perkValue.maxSquads
        }
        
        if (perkValue.maxTripsPerSquad) {
          additionalTripsPerSquad += perkValue.maxTripsPerSquad
        }
        
        if (perkValue.additionalDailyAI) {
          additionalDailyAI += perkValue.additionalDailyAI
        }
        
        if (perkValue.additionalWeeklyAI) {
          additionalWeeklyAI += perkValue.additionalWeeklyAI
        }
      }

      return {
        additionalSquads,
        additionalTripsPerSquad,
        additionalDailyAI,
        additionalWeeklyAI,
        activePerkIds
      }
    } catch (error) {
      console.error("Error calculating perk enhancements:", error)
      return {
        additionalSquads: 0,
        additionalTripsPerSquad: 0,
        additionalDailyAI: 0,
        additionalWeeklyAI: 0,
        activePerkIds: []
      }
    }
  }

  /**
   * Check if a user has any active subscription perks
   */
  static async hasActiveSubscriptionPerks(userId: string): Promise<boolean> {
    try {
      const perkEntries = await UserSubscriptionEntryService.getEntriesBySource(userId, "perk")
      return perkEntries.some(entry => entry.status === "applied")
    } catch (error) {
      console.error("Error checking active subscription perks:", error)
      return false
    }
  }

  /**
   * Get subscription perk summary
   */
  static async getSubscriptionPerkSummary(userId: string): Promise<{
    totalPerks: number
    activePerks: number
    expiredPerks: number
    upcomingExpirations: Array<{
      perkId: string
      perkName: string
      expiresAt: Date
    }>
  }> {
    try {
      const activePerkSubscriptions = await this.getActivePerkSubscriptions(userId)
      const totalPerks = activePerkSubscriptions.length
      const activePerks = activePerkSubscriptions.filter(p => p.status === "applied").length
      const expiredPerks = activePerkSubscriptions.filter(p => p.status === "expired").length

      // Get upcoming expirations (within 7 days)
      const sevenDaysFromNow = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)
      const upcomingExpirations = activePerkSubscriptions
        .filter(p => p.expiresAt && p.expiresAt <= sevenDaysFromNow && p.status === "applied")
        .map(p => ({
          perkId: p.perkId,
          perkName: p.perkName,
          expiresAt: p.expiresAt!
        }))
        .sort((a, b) => a.expiresAt.getTime() - b.expiresAt.getTime())

      return {
        totalPerks,
        activePerks,
        expiredPerks,
        upcomingExpirations
      }
    } catch (error) {
      console.error("Error getting subscription perk summary:", error)
      return {
        totalPerks: 0,
        activePerks: 0,
        expiredPerks: 0,
        upcomingExpirations: []
      }
    }
  }

  /**
   * Cleanup expired perk subscription entries
   */
  static async cleanupExpiredPerkEntries(userId: string): Promise<ServiceResponse> {
    try {
      const perkEntries = await UserSubscriptionEntryService.getEntriesBySource(userId, "perk")
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      let cleanedCount = 0

      for (const entry of perkEntries) {
        if (entry.status === "expired") {
          const updatedAt = entry.updatedAt instanceof Date 
            ? entry.updatedAt 
            : entry.updatedAt?.toDate()

          if (updatedAt && updatedAt < thirtyDaysAgo) {
            const deleteResult = await UserSubscriptionEntryService.deleteSubscriptionEntry(
              userId, 
              entry.id
            )
            
            if (deleteResult.success) {
              cleanedCount++
            }
          }
        }
      }

      console.log(`Cleaned up ${cleanedCount} expired perk subscription entries for user ${userId}`)
      return { success: true }
    } catch (error) {
      console.error("Error cleaning up expired perk entries:", error)
      return { success: false, error }
    }
  }
}
