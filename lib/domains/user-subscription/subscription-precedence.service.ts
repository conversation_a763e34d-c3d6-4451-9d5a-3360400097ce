import { runTransaction } from "firebase/firestore"
import { db } from "@/lib/firebase"
import { ServiceResponse } from "../base/base.types"
import {
  UserSubscriptionEntry,
  UserSubscriptionEntryCreateData,
  SubscriptionSource,
  SubscriptionEntryStatus,
  getActiveEntry,
  isEntryActive,
  sortEntriesByPrecedence,
  SUBSCRIPTION_PRECEDENCE
} from "./user-subscription.types"
import { UserSubscriptionEntryService } from "./user-subscription-entry.service"

/**
 * Service for managing subscription precedence and status transitions
 */
export class SubscriptionPrecedenceService {
  
  /**
   * Add a new subscription entry and recalculate precedence
   */
  static async addSubscriptionWithPrecedence(
    entryData: UserSubscriptionEntryCreateData
  ): Promise<ServiceResponse<UserSubscriptionEntry>> {
    try {
      return await runTransaction(db, async (transaction) => {
        // Get all existing entries for the user
        const existingEntries = await UserSubscriptionEntryService.getUserSubscriptionEntries(entryData.userId)
        
        // Create the new entry
        const createResult = await UserSubscriptionEntryService.createSubscriptionEntry(entryData)
        if (!createResult.success || !createResult.data) {
          throw new Error(createResult.error?.message || "Failed to create subscription entry")
        }

        const newEntry = createResult.data
        
        // Recalculate precedence with the new entry
        await this.recalculatePrecedence(entryData.userId, [...existingEntries, newEntry])
        
        return createResult
      })
    } catch (error) {
      console.error("Error adding subscription with precedence:", error)
      return { success: false, error }
    }
  }

  /**
   * Remove a subscription entry and recalculate precedence
   */
  static async removeSubscriptionWithPrecedence(
    userId: string,
    entryId: string
  ): Promise<ServiceResponse> {
    try {
      return await runTransaction(db, async (transaction) => {
        // Delete the entry
        const deleteResult = await UserSubscriptionEntryService.deleteSubscriptionEntry(userId, entryId)
        if (!deleteResult.success) {
          throw new Error(deleteResult.error?.message || "Failed to delete subscription entry")
        }

        // Get remaining entries and recalculate precedence
        const remainingEntries = await UserSubscriptionEntryService.getUserSubscriptionEntries(userId)
        await this.recalculatePrecedence(userId, remainingEntries)
        
        return deleteResult
      })
    } catch (error) {
      console.error("Error removing subscription with precedence:", error)
      return { success: false, error }
    }
  }

  /**
   * Recalculate precedence for all user subscription entries
   */
  static async recalculatePrecedence(
    userId: string,
    entries?: UserSubscriptionEntry[]
  ): Promise<ServiceResponse> {
    try {
      // Get entries if not provided
      const allEntries = entries || await UserSubscriptionEntryService.getUserSubscriptionEntries(userId)
      
      // Sort entries by precedence (highest first)
      const sortedEntries = sortEntriesByPrecedence(allEntries)
      
      // Find the highest precedence active entry
      const activeEntry = getActiveEntry(sortedEntries)
      
      // Update status for all entries
      for (const entry of allEntries) {
        let newStatus: SubscriptionEntryStatus = entry.status
        
        if (!isEntryActive(entry)) {
          // Entry is not in valid time range or already expired
          newStatus = "expired"
        } else if (activeEntry && entry.id === activeEntry.id) {
          // This is the highest precedence active entry
          newStatus = "applied"
        } else if (isEntryActive(entry)) {
          // Entry is active but lower precedence
          newStatus = "paused"
        }
        
        // Update status if it changed
        if (newStatus !== entry.status) {
          await UserSubscriptionEntryService.updateEntryStatus(userId, entry.id, newStatus)
        }
      }

      // Handle Stripe subscription pausing/resumption
      await this.handleStripeSubscriptionStatus(userId, allEntries, activeEntry)
      
      return { success: true }
    } catch (error) {
      console.error("Error recalculating precedence:", error)
      return { success: false, error }
    }
  }

  /**
   * Handle Stripe subscription pausing/resumption based on precedence
   */
  private static async handleStripeSubscriptionStatus(
    userId: string,
    allEntries: UserSubscriptionEntry[],
    activeEntry: UserSubscriptionEntry | null
  ): Promise<void> {
    try {
      const stripeEntries = allEntries.filter(entry => entry.source === "stripe")
      
      for (const stripeEntry of stripeEntries) {
        const shouldBePaused = activeEntry && 
                              activeEntry.source !== "stripe" && 
                              activeEntry.precedence < stripeEntry.precedence
        
        if (shouldBePaused && stripeEntry.status === "applied") {
          // Pause Stripe subscription
          await this.pauseStripeSubscription(userId, stripeEntry.id)
        } else if (!shouldBePaused && stripeEntry.status === "paused" && isEntryActive(stripeEntry)) {
          // Resume Stripe subscription
          await this.resumeStripeSubscription(userId, stripeEntry.id)
        }
      }
    } catch (error) {
      console.error("Error handling Stripe subscription status:", error)
    }
  }

  /**
   * Pause a Stripe subscription (update status, don't cancel in Stripe)
   */
  private static async pauseStripeSubscription(
    userId: string,
    entryId: string
  ): Promise<void> {
    try {
      await UserSubscriptionEntryService.updateEntryStatus(userId, entryId, "paused")
      console.log(`Paused Stripe subscription entry ${entryId} for user ${userId}`)
      
      // TODO: Implement actual Stripe subscription pausing via API
      // This would involve calling Stripe API to pause the subscription
      // For now, we just update the local status
    } catch (error) {
      console.error("Error pausing Stripe subscription:", error)
    }
  }

  /**
   * Resume a Stripe subscription
   */
  private static async resumeStripeSubscription(
    userId: string,
    entryId: string
  ): Promise<void> {
    try {
      await UserSubscriptionEntryService.updateEntryStatus(userId, entryId, "applied")
      console.log(`Resumed Stripe subscription entry ${entryId} for user ${userId}`)
      
      // TODO: Implement actual Stripe subscription resumption via API
      // This would involve calling Stripe API to resume the subscription
      // For now, we just update the local status
    } catch (error) {
      console.error("Error resuming Stripe subscription:", error)
    }
  }

  /**
   * Get subscription precedence summary for a user
   */
  static async getSubscriptionPrecedenceSummary(userId: string): Promise<{
    activeEntry: UserSubscriptionEntry | null
    pausedEntries: UserSubscriptionEntry[]
    expiredEntries: UserSubscriptionEntry[]
    pendingEntries: UserSubscriptionEntry[]
  }> {
    try {
      const allEntries = await UserSubscriptionEntryService.getUserSubscriptionEntries(userId)
      
      return {
        activeEntry: allEntries.find(entry => entry.status === "applied") || null,
        pausedEntries: allEntries.filter(entry => entry.status === "paused"),
        expiredEntries: allEntries.filter(entry => entry.status === "expired"),
        pendingEntries: allEntries.filter(entry => entry.status === "pending")
      }
    } catch (error) {
      console.error("Error getting subscription precedence summary:", error)
      return {
        activeEntry: null,
        pausedEntries: [],
        expiredEntries: [],
        pendingEntries: []
      }
    }
  }

  /**
   * Force recalculation of precedence for a user (useful for cron jobs)
   */
  static async forceRecalculatePrecedence(userId: string): Promise<ServiceResponse> {
    try {
      // Mark expired entries first
      await UserSubscriptionEntryService.markExpiredEntries(userId)
      
      // Then recalculate precedence
      return await this.recalculatePrecedence(userId)
    } catch (error) {
      console.error("Error force recalculating precedence:", error)
      return { success: false, error }
    }
  }

  /**
   * Check if a user has any active subscription (from any source)
   */
  static async hasActiveSubscription(userId: string): Promise<boolean> {
    try {
      const aggregatedState = await UserSubscriptionEntryService.getAggregatedSubscriptionState(userId)
      return aggregatedState.isSubscribed
    } catch (error) {
      console.error("Error checking active subscription:", error)
      return false
    }
  }

  /**
   * Get the current subscription source for a user
   */
  static async getCurrentSubscriptionSource(userId: string): Promise<SubscriptionSource | null> {
    try {
      const aggregatedState = await UserSubscriptionEntryService.getAggregatedSubscriptionState(userId)
      return aggregatedState.activeEntry?.source || null
    } catch (error) {
      console.error("Error getting current subscription source:", error)
      return null
    }
  }
}
