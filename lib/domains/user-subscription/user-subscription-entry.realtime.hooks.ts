"use client"

import { useEffect, useState, useCallback } from "react"
import { 
  collection, 
  onSnapshot, 
  query, 
  orderBy,
  Unsubscribe 
} from "firebase/firestore"
import { db } from "@/lib/firebase"
import { 
  UserSubscriptionEntry,
  AggregatedSubscriptionState 
} from "./user-subscription.types"
import { UserSubscriptionEntryService } from "./user-subscription-entry.service"
import { SubscriptionAggregationService } from "./subscription-aggregation.service"

/**
 * Hook for real-time subscription entries
 */
export function useUserSubscriptionEntriesRealtime(userId: string | null) {
  const [entries, setEntries] = useState<UserSubscriptionEntry[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    if (!userId) {
      setEntries([])
      setLoading(false)
      setError(null)
      return
    }

    setLoading(true)
    setError(null)

    const entriesRef = collection(db, "userSubscriptions", userId, "entries")
    const q = query(entriesRef, orderBy("precedence", "asc"))

    const unsubscribe = onSnapshot(
      q,
      (snapshot) => {
        try {
          const entriesData = snapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })) as UserSubscriptionEntry[]

          setEntries(entriesData)
          setLoading(false)
          setError(null)
        } catch (err) {
          console.error("Error processing subscription entries snapshot:", err)
          setError(err as Error)
          setLoading(false)
        }
      },
      (err) => {
        console.error("Error in subscription entries subscription:", err)
        setError(err as Error)
        setLoading(false)
      }
    )

    return () => unsubscribe()
  }, [userId])

  return { entries, loading, error }
}

/**
 * Hook for real-time aggregated subscription state
 */
export function useAggregatedSubscriptionStateRealtime(userId: string | null) {
  const [aggregatedState, setAggregatedState] = useState<AggregatedSubscriptionState | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  const refreshAggregatedState = useCallback(async () => {
    if (!userId) {
      setAggregatedState(null)
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      const state = await SubscriptionAggregationService.getComprehensiveSubscriptionState(userId)
      setAggregatedState(state)
      setError(null)
    } catch (err) {
      console.error("Error fetching aggregated subscription state:", err)
      setError(err as Error)
    } finally {
      setLoading(false)
    }
  }, [userId])

  // Subscribe to entries changes and refresh aggregated state
  const { entries } = useUserSubscriptionEntriesRealtime(userId)

  useEffect(() => {
    refreshAggregatedState()
  }, [refreshAggregatedState, entries])

  return { 
    aggregatedState, 
    loading, 
    error, 
    refresh: refreshAggregatedState 
  }
}

/**
 * Hook for real-time subscription summary
 */
export function useSubscriptionSummaryRealtime(userId: string | null) {
  const [summary, setSummary] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  const refreshSummary = useCallback(async () => {
    if (!userId) {
      setSummary(null)
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      const summaryData = await SubscriptionAggregationService.getSubscriptionSummary(userId)
      setSummary(summaryData)
      setError(null)
    } catch (err) {
      console.error("Error fetching subscription summary:", err)
      setError(err as Error)
    } finally {
      setLoading(false)
    }
  }, [userId])

  // Subscribe to entries changes and refresh summary
  const { entries } = useUserSubscriptionEntriesRealtime(userId)

  useEffect(() => {
    refreshSummary()
  }, [refreshSummary, entries])

  return { 
    summary, 
    loading, 
    error, 
    refresh: refreshSummary 
  }
}

/**
 * Hook for real-time subscription limits (perk-aware)
 */
export function useSubscriptionLimitsRealtime(userId: string | null) {
  const [limits, setLimits] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  const refreshLimits = useCallback(async () => {
    if (!userId) {
      setLimits(null)
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      const limitsData = await SubscriptionAggregationService.getEnhancedSubscriptionLimits(userId)
      setLimits(limitsData)
      setError(null)
    } catch (err) {
      console.error("Error fetching subscription limits:", err)
      setError(err as Error)
    } finally {
      setLoading(false)
    }
  }, [userId])

  // Subscribe to entries changes and refresh limits
  const { entries } = useUserSubscriptionEntriesRealtime(userId)

  useEffect(() => {
    refreshLimits()
  }, [refreshLimits, entries])

  return { 
    limits, 
    loading, 
    error, 
    refresh: refreshLimits 
  }
}

/**
 * Comprehensive hook that combines all subscription data
 */
export function useComprehensiveSubscriptionRealtime(userId: string | null) {
  const { entries, loading: entriesLoading, error: entriesError } = useUserSubscriptionEntriesRealtime(userId)
  const { aggregatedState, loading: stateLoading, error: stateError } = useAggregatedSubscriptionStateRealtime(userId)
  const { summary, loading: summaryLoading, error: summaryError } = useSubscriptionSummaryRealtime(userId)
  const { limits, loading: limitsLoading, error: limitsError } = useSubscriptionLimitsRealtime(userId)

  const loading = entriesLoading || stateLoading || summaryLoading || limitsLoading
  const error = entriesError || stateError || summaryError || limitsError

  return {
    entries,
    aggregatedState,
    summary,
    limits,
    loading,
    error,
    // Computed values
    isSubscribed: aggregatedState?.isSubscribed || false,
    subscriptionPlan: aggregatedState?.subscriptionPlan || "free",
    activeEntry: aggregatedState?.activeEntry || null,
    currentSource: summary?.currentSource || null,
    expiresAt: summary?.expiresAt || null,
    nextBillingDate: summary?.nextBillingDate || null,
    pausedSubscriptions: summary?.pausedSubscriptions || [],
    upcomingExpirations: summary?.upcomingExpirations || [],
    // Limits
    maxSquads: limits?.maxSquads || 1,
    maxTripsPerSquad: limits?.maxTripsPerSquad || 2,
    maxDailyAIRequests: limits?.maxDailyAIRequests || 10,
    maxWeeklyAIRequests: limits?.maxWeeklyAIRequests || 50,
    hasTripChat: limits?.hasTripChat || false,
    perkEnhancements: limits?.perkEnhancements || null,
  }
}

/**
 * Hook for checking feature access in real-time
 */
export function useFeatureAccessRealtime(userId: string | null, feature: string) {
  const [hasAccess, setHasAccess] = useState(false)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  const checkAccess = useCallback(async () => {
    if (!userId) {
      setHasAccess(false)
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      const access = await SubscriptionAggregationService.hasFeatureAccess(userId, feature)
      setHasAccess(access)
      setError(null)
    } catch (err) {
      console.error("Error checking feature access:", err)
      setError(err as Error)
      setHasAccess(false)
    } finally {
      setLoading(false)
    }
  }, [userId, feature])

  // Subscribe to entries changes and recheck access
  const { entries } = useUserSubscriptionEntriesRealtime(userId)

  useEffect(() => {
    checkAccess()
  }, [checkAccess, entries])

  return { 
    hasAccess, 
    loading, 
    error, 
    refresh: checkAccess 
  }
}
