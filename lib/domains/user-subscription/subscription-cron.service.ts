import { 
  collection, 
  getDocs, 
  doc, 
  setDoc,
  serverTimestamp,
  query,
  where,
  limit
} from "firebase/firestore"
import { db } from "@/lib/firebase"
import { ServiceResponse } from "../base/base.types"
import { UserSubscriptionEntryService } from "./user-subscription-entry.service"
import { SubscriptionPrecedenceService } from "./subscription-precedence.service"
import { PerkSubscriptionService } from "./perk-subscription.service"
import { SubscriptionAggregationService } from "./subscription-aggregation.service"
import { StripeSubscriptionService } from "./stripe-subscription.service"

/**
 * Service for handling subscription-related cron jobs
 */
export class SubscriptionCronService {
  private static readonly CRON_LOG_COLLECTION = "cronLogs"
  private static readonly BATCH_SIZE = 50 // Process users in batches

  /**
   * Main cron job to process subscription expirations and precedence
   */
  static async processSubscriptionExpirations(): Promise<ServiceResponse<{
    processedUsers: number
    expiredEntries: number
    precedenceUpdates: number
    errors: string[]
    duration: number
  }>> {
    const startTime = Date.now()
    
    try {
      console.log("🕐 Starting subscription expiration processing...")
      
      const errors: string[] = []
      let processedUsers = 0
      let expiredEntries = 0
      let precedenceUpdates = 0

      // Get all users with subscription entries
      const usersWithSubscriptions = await this.getUsersWithSubscriptions()
      
      console.log(`📊 Found ${usersWithSubscriptions.length} users with subscriptions`)

      // Process users in batches
      for (let i = 0; i < usersWithSubscriptions.length; i += this.BATCH_SIZE) {
        const batch = usersWithSubscriptions.slice(i, i + this.BATCH_SIZE)
        
        for (const userId of batch) {
          try {
            const result = await this.processUserSubscriptions(userId)
            
            processedUsers++
            expiredEntries += result.expiredCount
            
            if (result.precedenceChanged) {
              precedenceUpdates++
            }
            
            if (result.errors.length > 0) {
              errors.push(...result.errors.map(err => `User ${userId}: ${err}`))
            }
          } catch (error) {
            const errorMsg = `Failed to process user ${userId}: ${error}`
            console.error(errorMsg)
            errors.push(errorMsg)
          }
        }

        // Small delay between batches to avoid overwhelming the database
        if (i + this.BATCH_SIZE < usersWithSubscriptions.length) {
          await new Promise(resolve => setTimeout(resolve, 100))
        }
      }

      const duration = Date.now() - startTime
      
      // Log cron job execution
      await this.logCronExecution("subscription_expiration", {
        processedUsers,
        expiredEntries,
        precedenceUpdates,
        errorCount: errors.length,
        duration
      })

      console.log(`✅ Subscription expiration processing completed in ${duration}ms`)
      console.log(`📊 Processed: ${processedUsers} users, ${expiredEntries} expired entries, ${precedenceUpdates} precedence updates`)
      
      if (errors.length > 0) {
        console.log(`⚠️  ${errors.length} errors occurred`)
      }

      return {
        success: true,
        data: {
          processedUsers,
          expiredEntries,
          precedenceUpdates,
          errors,
          duration
        }
      }
    } catch (error) {
      const duration = Date.now() - startTime
      console.error("❌ Subscription expiration processing failed:", error)
      
      await this.logCronExecution("subscription_expiration", {
        processedUsers: 0,
        expiredEntries: 0,
        precedenceUpdates: 0,
        errorCount: 1,
        duration,
        error: error instanceof Error ? error.message : "Unknown error"
      })

      return { success: false, error }
    }
  }

  /**
   * Process subscriptions for a single user
   */
  private static async processUserSubscriptions(userId: string): Promise<{
    expiredCount: number
    precedenceChanged: boolean
    errors: string[]
  }> {
    const errors: string[] = []
    let expiredCount = 0
    let precedenceChanged = false

    try {
      // Get current subscription state
      const beforeState = await UserSubscriptionEntryService.getAggregatedSubscriptionState(userId)
      const beforeActiveEntry = beforeState.activeEntry

      // Process expired perk subscriptions
      const perkResult = await PerkSubscriptionService.processExpiredPerkSubscriptions(userId)
      expiredCount += perkResult.expiredCount
      errors.push(...perkResult.errors)

      // Mark expired entries
      const expiredEntryIds = await UserSubscriptionEntryService.markExpiredEntries(userId)
      expiredCount += expiredEntryIds.length

      // Force recalculate precedence
      const precedenceResult = await SubscriptionPrecedenceService.forceRecalculatePrecedence(userId)
      if (!precedenceResult.success) {
        errors.push(`Failed to recalculate precedence: ${precedenceResult.error?.message}`)
      }

      // Check if active subscription changed
      const afterState = await UserSubscriptionEntryService.getAggregatedSubscriptionState(userId)
      const afterActiveEntry = afterState.activeEntry

      precedenceChanged = beforeActiveEntry?.id !== afterActiveEntry?.id

      // If precedence changed and there's a Stripe subscription, handle pausing/resumption
      if (precedenceChanged) {
        await this.handleStripeSubscriptionStatusChange(userId, beforeActiveEntry, afterActiveEntry)
      }

      return { expiredCount, precedenceChanged, errors }
    } catch (error) {
      errors.push(`Exception during processing: ${error}`)
      return { expiredCount, precedenceChanged, errors }
    }
  }

  /**
   * Handle Stripe subscription status changes due to precedence changes
   */
  private static async handleStripeSubscriptionStatusChange(
    userId: string,
    beforeActiveEntry: any,
    afterActiveEntry: any
  ): Promise<void> {
    try {
      // Get all user entries to check for Stripe subscriptions
      const allEntries = await UserSubscriptionEntryService.getUserSubscriptionEntries(userId)
      const stripeEntries = allEntries.filter(entry => entry.source === "stripe")

      for (const stripeEntry of stripeEntries) {
        const wasActive = beforeActiveEntry?.id === stripeEntry.id
        const isActive = afterActiveEntry?.id === stripeEntry.id

        if (wasActive && !isActive) {
          // Stripe subscription was active but now should be paused
          await StripeSubscriptionService.pauseStripeSubscription(userId, stripeEntry.id)
          console.log(`Paused Stripe subscription for user ${userId} due to higher precedence subscription`)
        } else if (!wasActive && isActive) {
          // Stripe subscription should now be active
          await StripeSubscriptionService.resumeStripeSubscription(userId, stripeEntry.id)
          console.log(`Resumed Stripe subscription for user ${userId} due to precedence change`)
        }
      }
    } catch (error) {
      console.error(`Error handling Stripe subscription status change for user ${userId}:`, error)
    }
  }

  /**
   * Cleanup expired subscription entries (run weekly)
   */
  static async cleanupExpiredEntries(): Promise<ServiceResponse<{
    processedUsers: number
    cleanedEntries: number
    errors: string[]
    duration: number
  }>> {
    const startTime = Date.now()
    
    try {
      console.log("🧹 Starting expired subscription entries cleanup...")
      
      const errors: string[] = []
      let processedUsers = 0
      let cleanedEntries = 0

      const usersWithSubscriptions = await this.getUsersWithSubscriptions()
      
      for (const userId of usersWithSubscriptions) {
        try {
          const result = await SubscriptionAggregationService.cleanupExpiredEntries(userId)
          processedUsers++
          cleanedEntries += result.cleanedCount
          errors.push(...result.errors)
        } catch (error) {
          errors.push(`Failed to cleanup entries for user ${userId}: ${error}`)
        }
      }

      const duration = Date.now() - startTime
      
      await this.logCronExecution("cleanup_expired_entries", {
        processedUsers,
        cleanedEntries,
        errorCount: errors.length,
        duration
      })

      console.log(`✅ Cleanup completed: ${cleanedEntries} entries cleaned from ${processedUsers} users`)

      return {
        success: true,
        data: {
          processedUsers,
          cleanedEntries,
          errors,
          duration
        }
      }
    } catch (error) {
      const duration = Date.now() - startTime
      console.error("❌ Cleanup failed:", error)
      
      await this.logCronExecution("cleanup_expired_entries", {
        processedUsers: 0,
        cleanedEntries: 0,
        errorCount: 1,
        duration,
        error: error instanceof Error ? error.message : "Unknown error"
      })

      return { success: false, error }
    }
  }

  /**
   * Health check for subscription system
   */
  static async performHealthCheck(): Promise<ServiceResponse<{
    totalUsers: number
    usersWithIssues: number
    commonIssues: Array<{
      issue: string
      count: number
    }>
    systemHealth: "healthy" | "warning" | "critical"
  }>> {
    try {
      console.log("🏥 Performing subscription system health check...")
      
      const usersWithSubscriptions = await this.getUsersWithSubscriptions()
      const issueMap = new Map<string, number>()
      let usersWithIssues = 0

      // Sample a subset of users for health check to avoid performance issues
      const sampleSize = Math.min(100, usersWithSubscriptions.length)
      const sampleUsers = usersWithSubscriptions.slice(0, sampleSize)

      for (const userId of sampleUsers) {
        try {
          const healthResult = await SubscriptionAggregationService.checkSubscriptionHealth(userId)
          
          if (!healthResult.isHealthy) {
            usersWithIssues++
            
            // Count common issues
            healthResult.warnings.forEach(warning => {
              const count = issueMap.get(warning) || 0
              issueMap.set(warning, count + 1)
            })
          }
        } catch (error) {
          usersWithIssues++
          const issue = "Health check failed"
          const count = issueMap.get(issue) || 0
          issueMap.set(issue, count + 1)
        }
      }

      const commonIssues = Array.from(issueMap.entries())
        .map(([issue, count]) => ({ issue, count }))
        .sort((a, b) => b.count - a.count)

      // Determine system health
      const issueRate = usersWithIssues / sampleUsers.length
      let systemHealth: "healthy" | "warning" | "critical"
      
      if (issueRate < 0.05) {
        systemHealth = "healthy"
      } else if (issueRate < 0.2) {
        systemHealth = "warning"
      } else {
        systemHealth = "critical"
      }

      await this.logCronExecution("health_check", {
        totalUsers: usersWithSubscriptions.length,
        sampleSize,
        usersWithIssues,
        issueRate,
        systemHealth
      })

      console.log(`🏥 Health check completed: ${systemHealth} (${usersWithIssues}/${sampleSize} users with issues)`)

      return {
        success: true,
        data: {
          totalUsers: usersWithSubscriptions.length,
          usersWithIssues,
          commonIssues,
          systemHealth
        }
      }
    } catch (error) {
      console.error("❌ Health check failed:", error)
      return { success: false, error }
    }
  }

  /**
   * Get list of users with subscription entries
   */
  private static async getUsersWithSubscriptions(): Promise<string[]> {
    try {
      // Get all user subscription documents
      const subscriptionsSnapshot = await getDocs(collection(db, "userSubscriptions"))
      return subscriptionsSnapshot.docs.map(doc => doc.id)
    } catch (error) {
      console.error("Error getting users with subscriptions:", error)
      return []
    }
  }

  /**
   * Log cron job execution
   */
  private static async logCronExecution(jobType: string, data: any): Promise<void> {
    try {
      const logDoc = doc(collection(db, this.CRON_LOG_COLLECTION))
      await setDoc(logDoc, {
        jobType,
        executedAt: serverTimestamp(),
        ...data
      })
    } catch (error) {
      console.error("Error logging cron execution:", error)
    }
  }
}
